.client-page-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%
}

.portal-templates-content {
  padding: 10px 0;
}

.templates-divider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 20px 0;
}

.template-selection-row {
  display: flex;
  align-items: center;
  padding: 0 8px;

  .template-selection-label {
    min-width: 200px;
  }

  .template-selection-dropdown {
    flex: 1;
    max-width: 400px;
  }

  .template-selection-button {
    margin-left: auto;
  }
}

.folder-structure-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;

  .folder-structure-header {
    background: #f5f5f5;
    padding: 10px 20px;
    border-bottom: 1px solid #e0e0e0;
  }

  .folder-structure-title {
    margin: 0;
  }

  .folder-structure-content {
    height: 190px;
    overflow-y: auto;
    padding: 20px 24px;
  }
}

.clients-footer-buttons {
  display: flex;
  gap: 12px;
}

.contacts-content {
  padding: 20px;
  text-align: center;
  color: #6b7280;

  .client-info {
    text-align: left;
    margin-bottom: 20px;

    h4 {
      margin: 0;
      color: #374151;
      font-weight: 600;
    }
  }
}

.k-tabstrip-content {
  .k-animation-container,
  .k-animation-container-relative {
    width: 100%;
    padding: 10px;
  }
}
