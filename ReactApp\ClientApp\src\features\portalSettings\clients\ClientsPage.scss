.client-page-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

// Styles moved from templates for AssignClientsTabStrip
.portal-templates-content {
  padding: 10px 0;
  height: 400px;
}

.client-info-section {
  padding: 10px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 10px;

  h4 {
    margin: 0;
    color: #374151;
    font-weight: 600;
  }
}

.templates-divider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 20px 0;
}

.template-selection-row {
  display: flex;
  align-items: center;
  padding: 0 8px;

  .template-selection-label {
    font-weight: 500;
    color: #374151;
    min-width: 200px;
    flex-shrink: 0;
  }

  .template-selection-dropdown {
    flex: 1;
    min-width: 250px;
    max-width: 400px;
  }

  .template-selection-button {
    margin-left: auto;
  }
}

.folder-structure-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;

  .folder-structure-header {
    background: #f5f5f5;
    padding: 10px 20px;
    border-bottom: 1px solid #e0e0e0;
  }

  .folder-structure-title {
    margin: 0;
  }

  .folder-structure-content {
    color: #6b7280;
    height: 190px;
    overflow-y: auto;
    padding: 20px 24px;
  }

  .no-template-message {
    color: #b91c1c;
    font-weight: 500;
    font-size: 15px;
    text-align: center;
    margin: 24px 0;
  }
}

.clients-footer-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

// TabStrip width fix
.assign-clients-tabstrip {
  width: 100%;

  .k-tabstrip {
    width: 100%;

    .k-tabstrip-items-wrapper {
      width: 100%;
    }

    .k-tabstrip-content {
      width: 100%;
      padding: 0;
    }
  }
}

.contacts-content {
  padding: 20px;
  text-align: center;
  color: #6b7280;

  .client-info {
    text-align: left;
    margin-bottom: 20px;

    h4 {
      margin: 0;
      color: #374151;
      font-weight: 600;
    }
  }
}

.k-tabstrip-content {
  .k-animation-container,
  .k-animation-container-relative {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
}

// Clickable grid rows
.clickable-cell {
  cursor: pointer !important;

  &:hover {
    background-color: #f5f5f5 !important;
  }
}