interface ContactsTabProps {
  selectedClient?: any; // Client data passed from the grid
}

export default function ContactsTab({ selectedClient }: ContactsTabProps) {
  return (
    <div className="contacts-content">
      {selectedClient && (
        <div className="client-info">
          <h4>Client: {selectedClient.clientName || selectedClient.name}</h4>
        </div>
      )}
      <p>Contacts tab content - placeholder</p>
    </div>
  );
}
