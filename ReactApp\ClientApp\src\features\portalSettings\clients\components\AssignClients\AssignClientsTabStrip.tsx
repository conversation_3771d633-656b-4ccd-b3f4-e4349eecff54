import { useTranslation } from "react-i18next";
import { TabStrip, TabStripTab } from "@progress/kendo-react-layout";
import PortalTemplatesTab from "./PortalTemplatesTab";
import ContactsTab from "./ContactsTab";

interface AssignClientsTabStripProps {
  activeTab: number;
  showAlert: boolean;
  selectedTemplate: string | null;
  templateOptions: { text: string; value: string }[];
  handleTabSelect: (e: any) => void;
  handleTemplateChange: (e: any) => void;
  handleClearSelection: () => void;
  handleAlertClose: () => void;
  selectedClient?: any; // Client data passed from the grid
}

export default function AssignClientsTabStrip({
  activeTab,
  showAlert,
  selectedTemplate,
  templateOptions,
  handleTabSelect,
  handleTemplateChange,
  handleClearSelection,
  handleAlertClose,
  selectedClient,
}: AssignClientsTabStripProps) {
  const { t } = useTranslation("dashboard");

  return (
    <div className="assign-clients-tabstrip">
      <TabStrip selected={activeTab} onSelect={handleTabSelect}>
        <TabStripTab title={t("AssignClients_tab_title")}> 
          <PortalTemplatesTab
            showAlert={showAlert}
            selectedTemplate={selectedTemplate}
            templateOptions={templateOptions}
            onAlertClose={handleAlertClose}
            onTemplateChange={handleTemplateChange}
            onClearSelection={handleClearSelection}
            selectedClient={selectedClient}
          />
        </TabStripTab>
        <TabStripTab title={t("AssignClients_contacts_tab_title")}> 
          <ContactsTab selectedClient={selectedClient} />
        </TabStripTab>
      </TabStrip>
    </div>
  );
}
