import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import useClientsList from "./hooks/useClientsList";
import AdvancedBaseGrid from "@/components/AdvancedBaseGrid/AdvancedBaseGrid";
import BaseGridFilterFactory from "@/components/BaseGridFilterFactory/BaseGridFilterFactory";
import GridNotice from "./components/GridNotice/GridNotice";
import AssignClientsTabStrip from "./components/AssignClients/AssignClientsTabStrip";
import ClientsFooter from "./components/AssignClients/ClientsFooter";
import { useClientsAssign } from "./hooks/useClientsAssign";
import { useState } from "react";

import "./ClientsPage.scss";

const ClientsPage = () => {
  const [showNotice, setShowNotice] = useState(true);
  const [selectedClient, setSelectedClient] = useState<any | null>(null);
  const [showTabStrip, setShowTabStrip] = useState(false);

  // Get client list data
  const {
    columns,
    isColumnsLoading,
    clientList,
    filters,
    sorts,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    handleSortChange,
    isLoading,
    isFetching,
    pagination,
    totalRecordCount,
  } = useClientsList();

  // Get tab strip data and handlers
  const {
    activeTab,
    showAlert,
    selectedTemplate,
    templateOptions,
    handleTabSelect,
    handleTemplateChange,
    handleClearSelection,
    handleAlertClose,
    handleFooterConfirm,
  } = useClientsAssign();

  const handleCloseNotice = () => {
    setShowNotice(false);
  };

  // Handle grid row click
  const handleRowClick = (client: any) => {
    setSelectedClient(client);
    setShowTabStrip(true);
  };

  // Handle cancel button click in footer
  const handleCancel = () => {
    setSelectedClient(null);
    setShowTabStrip(false);
  };

  // Handle confirm button click in footer
  const handleConfirm = () => {
    // Handle confirm action
    handleFooterConfirm();
    // Return to grid view
    setSelectedClient(null);
    setShowTabStrip(false);
  };

  // Create clickable cell mapper for all columns
  const createClickableCellMapper = () => {
    const cellMapper: Record<string, any> = {};
    columns.forEach((column) => {
      cellMapper[column.key] = (props: any) => (
        <td
          onClick={() => handleRowClick(props.dataItem)}
          style={{ cursor: 'pointer' }}
        >
          {props.dataItem[column.key]}
        </td>
      );
    });
    return cellMapper;
  };

  // Create the clickable cell mapper
  const dataCellMapper = createClickableCellMapper();

  return (
    <SectionLayout
      footer={showTabStrip ? (
        <ClientsFooter
          onCancel={handleCancel}
          onConfirm={handleConfirm}
          selectedTemplate={selectedTemplate}
        />
      ) : undefined}
    >
      <div className="client-page-content">
        {!showTabStrip ? (
          <>
            <GridNotice show={showNotice} onClose={handleCloseNotice} />
            <AdvancedBaseGrid
              totalRecordCount={totalRecordCount}
              columns={columns}
              dataSource={clientList}
              filters={filters}
              skip={pagination.skip}
              take={pagination.take}
              onFilterChange={handleFilterChange}
              onPageChange={handlePageChange}
              onSortChange={handleSortChange}
              onRefresh={handleRefresh}
              isLoading={isLoading || isFetching}
              sorts={sorts}
              isColumnsLoading={isColumnsLoading}
              dataCellMapper={dataCellMapper}
              renderFilterFactory={(props, column) => (
                <BaseGridFilterFactory
                  {...props}
                  column={column}
                  useAutoSuggestHook={() => ({
                    fetchSuggestions: async (_field: string, _value: string) => [],
                    isLoading: false,
                  })}
                />
              )}
            />
          </>
        ) : (
          <AssignClientsTabStrip
            activeTab={activeTab}
            showAlert={showAlert}
            selectedTemplate={selectedTemplate}
            templateOptions={templateOptions}
            handleTabSelect={handleTabSelect}
            handleTemplateChange={handleTemplateChange}
            handleClearSelection={handleClearSelection}
            handleAlertClose={handleAlertClose}
            selectedClient={selectedClient}
          />
        )}
      </div>
    </SectionLayout>
  );
};

export default ClientsPage;
