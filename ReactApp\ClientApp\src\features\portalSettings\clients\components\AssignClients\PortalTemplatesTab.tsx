import { useTranslation } from "react-i18next";
import { <PERSON><PERSON> } from "@progress/kendo-react-buttons";
import { DropDownList } from "@progress/kendo-react-dropdowns";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import AlertBox from "@/components/AlertBox/AlertBox";
import CreateTemplateTreeView from "./CreateTemplateTreeView";
import { mockTemplatesData } from "@/api/mocks/templatesMock";
import { useTemplateTreeView } from "../../hooks/useTemplateTreeView";
import { Loader } from "@progress/kendo-react-indicators";

interface TemplateOption {
  text: string;
  value: string;
}

interface PortalTemplatesTabProps {
  showAlert: boolean;
  selectedTemplate: string | null;
  templateOptions: TemplateOption[];
  onAlertClose: () => void;
  onTemplateChange: (_e: DropDownListChangeEvent) => void;
  onClearSelection: () => void;
  selectedClient?: any; // Client data passed from the grid
}

export default function PortalTemplatesTab({
  showAlert,
  selectedTemplate,
  templateOptions: _templateOptions, // ignore prop, use mock data
  onAlertClose,
  onTemplateChange,
  onClearSelection,
  selectedClient,
}: PortalTemplatesTabProps) {
  const { t } = useTranslation("dashboard");

  // Build dropdown options from mockTemplatesData
  const templateOptions: TemplateOption[] = mockTemplatesData.records.map(t => ({
    text: t.templateName,
    value: t.id,
  }));

  // Use the shared hook for folder structure and expand/collapse logic
  const {
    primaryFolders,
    validation: treeValidation,
    toggleExpand,
    isLoading: isTemplateLoading,
    isError: isTemplateError,
  } = useTemplateTreeView({
    templateId: selectedTemplate || undefined,
    isEditMode: true,
  });

  return (
    <div className="portal-templates-content">
      {/* AlertBox */}
      {showAlert && (
        <AlertBox
          message={t("portalTemplates_alert_message")}
          onClose={onAlertClose}
        />
      )}

      {/* Divider */}
      <div className="templates-divider" />

      {/* Template Selection Row */}
      <div className="template-selection-row">
        <div className="template-selection-label">
          {t("portalTemplates_template_label")}
        </div>
        <div className="template-selection-dropdown">
          <DropDownList
            data={mockTemplatesData.records}
            textField="templateName"
            dataItemKey="id"
            defaultItem={{ templateName: t("portalTemplates_select_placeholder") }}
            value={mockTemplatesData.records.find(option => option.id === selectedTemplate) || { templateName: t("portalTemplates_select_placeholder") }}
            onChange={onTemplateChange}
          />
        </div>
        <div className="template-selection-button">
          <Button
            onClick={onClearSelection}
            disabled={!selectedTemplate}
          >
            {t("portalTemplates_clear_selection")}
          </Button>
        </div>
      </div>

      {/* Second Divider */}
      <div className="templates-divider" />

      {/* Folder Structure Preview */}
      <div className="folder-structure-container">
        <div className="folder-structure-header">
          <p className="folder-structure-title">{t("portalTemplates_folder_structure_preview")}</p>
        </div>
        <div className="folder-structure-content">
          {isTemplateLoading ? (
            <div >
              <Loader size="large" />
            </div>
          ) : isTemplateError ? (
            <div>
              <p>Cannot Find Nodes for this template</p>
            </div>
          ) : selectedTemplate && primaryFolders.length > 0 ? (
            <CreateTemplateTreeView
              key={selectedTemplate}
              primaryFolders={primaryFolders}
              validation={treeValidation}
              canDeleteFolder={() => false}
              onAddPrimaryFolder={() => {}}
              onAddSecondaryFolder={() => {}}
              onEditFolder={() => {}}
              onSaveFolder={() => false}
              onCancelEdit={() => {}}
              onDeleteFolder={() => {}}
              onToggleExpand={toggleExpand}
              readOnly
            />
          ) : null}
        </div>
      </div>
    </div>
  );
}
